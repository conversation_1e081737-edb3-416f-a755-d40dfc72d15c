package com.example.hianime

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat

class MainActivity : AppCompatActivity() {
    
    private lateinit var webView: WebView
    
    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Enable full screen mode and hide system UI
        setupFullScreen()
        
        setContentView(R.layout.activity_main)
        
        webView = findViewById(R.id.webview)
        
        // Configure WebView settings
        setupWebView()
        
        // Load HiAnime website
        webView.loadUrl("https://hianime.to")
    }
    
    private fun setupFullScreen() {
        // Hide the status bar and navigation bar
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        windowInsetsController.let { controller ->
            controller.hide(WindowInsetsCompat.Type.systemBars())
            controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
        
        // Keep screen on while using the app
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        
        // Handle notch areas
        window.attributes.layoutInDisplayCutoutMode = 
            WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
    }
    
    @SuppressLint("SetJavaScriptEnabled")
    private fun setupWebView() {
        webView.settings.apply {
            // Enable JavaScript
            javaScriptEnabled = true
            
            // Enable DOM storage
            domStorageEnabled = true
            
            // Enable database storage
            databaseEnabled = true
            
            // App cache is deprecated, using default cache behavior
            
            // Set cache mode
            cacheMode = WebSettings.LOAD_DEFAULT
            
            // Enable zoom controls but hide zoom buttons
            setSupportZoom(true)
            builtInZoomControls = true
            displayZoomControls = false
            
            // Enable responsive design
            useWideViewPort = true
            loadWithOverviewMode = true
            
            // Set user agent to desktop to get full site
            userAgentString = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            
            // Enable mixed content (HTTP and HTTPS)
            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            
            // Enable hardware acceleration
            setRenderPriority(WebSettings.RenderPriority.HIGH)
            
            // Allow file access
            allowFileAccess = true
            allowContentAccess = true
            
            // Enable geolocation
            setGeolocationEnabled(true)

            // Block popups and unwanted content
            javaScriptCanOpenWindowsAutomatically = false
            setSupportMultipleWindows(false)
        }
        
        // Set WebViewClient to handle page navigation and block unwanted redirects
        webView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                return if (url != null) {
                    // Comprehensive ad blocking patterns
                    val adBlockPatterns = listOf(
                        // Ad networks
                        "googleads", "googlesyndication", "doubleclick", "adsystem",
                        "amazon-adsystem", "facebook.com/tr", "google-analytics",
                        "googletagmanager", "scorecardresearch", "quantserve",
                        "outbrain", "taboola", "revcontent", "mgid", "disqus",
                        // Popup patterns
                        "popunder", "popup", "pop-up", "interstitial",
                        // Redirect patterns
                        "redirect", "redir", "click", "track", "analytics",
                        // Ad-specific patterns
                        "/ads/", "/ad/", "advertisement", "banner", "sponsor",
                        // Fragment-only URLs (common popup trigger)
                        "/#", "/##", "javascript:", "about:blank",
                        // Suspicious domains
                        "bit.ly", "tinyurl", "short.link", "t.co"
                    )

                    // Check if URL contains ad/popup patterns
                    val isAdOrPopup = adBlockPatterns.any { pattern ->
                        url.contains(pattern, ignoreCase = true)
                    }

                    if (isAdOrPopup) {
                        // Completely block ad/popup URLs - don't load anything
                        return true
                    }

                    // Allow only hianime.to domain and its subdomains
                    val allowedDomains = listOf("hianime.to", "www.hianime.to")
                    val isAllowedDomain = allowedDomains.any { domain ->
                        url.contains(domain, ignoreCase = true)
                    }

                    if (isAllowedDomain) {
                        // Even for allowed domains, block fragment-only URLs (popups)
                        if (url.endsWith("/#") || url.endsWith("#") ||
                            url.contains("hianime.to/#") || url.contains("hianime.to#")) {
                            // Block these popup URLs completely
                            return true
                        }
                        // Allow legitimate hianime URLs to load
                        false
                    } else {
                        // For external domains, don't load and stay on current page
                        // This prevents blank screens
                        return true
                    }
                } else {
                    true // Block if URL is null
                }
            }

            override fun onPageStarted(view: WebView?, url: String?, favicon: android.graphics.Bitmap?) {
                super.onPageStarted(view, url, favicon)
                // Additional check when page starts loading
                if (url != null) {
                    val adBlockPatterns = listOf(
                        "googleads", "googlesyndication", "doubleclick", "adsystem",
                        "amazon-adsystem", "facebook.com/tr", "google-analytics",
                        "popunder", "popup", "redirect", "/ads/", "/ad/"
                    )

                    val isAdOrPopup = adBlockPatterns.any { pattern ->
                        url.contains(pattern, ignoreCase = true)
                    }

                    if (isAdOrPopup) {
                        // Stop loading the page
                        view?.stopLoading()
                    }
                }
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                // Inject comprehensive ad blocking and popup prevention
                view?.evaluateJavascript("""
                    (function() {
                        // Selective CSS ad blocking - preserve legitimate content
                        var style = document.createElement('style');
                        style.innerHTML = `
                            /* Basic layout fixes */
                            body { margin: 0 !important; padding: 0 !important; }

                            /* Hide specific ad elements without affecting anime covers */
                            .popup, .modal, .overlay:not(.anime-cover):not(.poster),
                            .advertisement, .ad-banner, .ads:not(.anime-list):not(.anime-grid),
                            .interstitial, .popunder, .pop-up,
                            [id*="popup"], [class*="popup"], [id*="modal"], [class*="modal"],
                            [id*="advertisement"], [class*="advertisement"],
                            [id*="sponsor"], [class*="sponsor"],
                            [id*="promo"]:not([class*="anime"]), [class*="promo"]:not([class*="anime"]),
                            iframe[src*="ads"], iframe[src*="doubleclick"], iframe[src*="googlesyndication"],
                            iframe[src*="amazon-adsystem"], iframe[src*="facebook.com/tr"],
                            script[src*="ads"], script[src*="doubleclick"], script[src*="googlesyndication"],
                            /* Block high z-index overlays but preserve legitimate modals */
                            div[style*="position: fixed"][style*="z-index: 999"]:not([class*="player"]):not([class*="video"]),
                            div[style*="position: fixed"][style*="z-index: 9999"]:not([class*="player"]):not([class*="video"]) {
                                display: none !important;
                                visibility: hidden !important;
                                opacity: 0 !important;
                                width: 0 !important;
                                height: 0 !important;
                            }

                            /* Ensure anime images and covers are visible */
                            img[src*="hianime"], .anime-cover img, .poster img,
                            .anime-item img, .film-poster img, .movie-item img {
                                display: block !important;
                                visibility: visible !important;
                                opacity: 1 !important;
                            }
                        `;
                        document.head.appendChild(style);

                        // Override and block popup functions
                        var originalOpen = window.open;
                        window.open = function(url, name, specs) {
                            console.log('Blocked popup attempt:', url);
                            return null;
                        };

                        // Block alert/confirm dialogs
                        window.alert = function(msg) {
                            console.log('Blocked alert:', msg);
                            return false;
                        };
                        window.confirm = function(msg) {
                            console.log('Blocked confirm:', msg);
                            return false;
                        };

                        // Block setTimeout/setInterval for ads
                        var originalSetTimeout = window.setTimeout;
                        var originalSetInterval = window.setInterval;

                        window.setTimeout = function(func, delay) {
                            if (typeof func === 'string' && (
                                func.includes('popup') || func.includes('ad') ||
                                func.includes('redirect') || func.includes('window.open')
                            )) {
                                console.log('Blocked setTimeout ad script');
                                return 0;
                            }
                            return originalSetTimeout.apply(this, arguments);
                        };

                        window.setInterval = function(func, delay) {
                            if (typeof func === 'string' && (
                                func.includes('popup') || func.includes('ad') ||
                                func.includes('redirect') || func.includes('window.open')
                            )) {
                                console.log('Blocked setInterval ad script');
                                return 0;
                            }
                            return originalSetInterval.apply(this, arguments);
                        };

                        // Block click events on suspicious links - enhanced for hianime.to/#
                        document.addEventListener('click', function(e) {
                            var target = e.target;
                            while (target && target !== document) {
                                var href = target.href || '';
                                var onclick = target.onclick ? target.onclick.toString() : '';

                                // Specifically block hianime.to/# and hianime.to# patterns
                                if (href.includes('hianime.to/#') || href.includes('hianime.to#') ||
                                    href.endsWith('/#') || href.endsWith('#') ||
                                    (href.includes('#') && href.split('#')[1] === '') ||
                                    href.includes('popup') || href.includes('redirect') ||
                                    onclick.includes('window.open') || onclick.includes('popup')) {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    e.stopImmediatePropagation();
                                    console.log('Blocked popup click:', href);
                                    return false;
                                }
                                target = target.parentElement;
                            }
                        }, true);

                        // Additional protection: Override link clicks that lead to fragments
                        var originalAddEventListener = Element.prototype.addEventListener;
                        Element.prototype.addEventListener = function(type, listener, options) {
                            if (type === 'click') {
                                var originalListener = listener;
                                listener = function(e) {
                                    var href = this.href || '';
                                    if (href.includes('hianime.to/#') || href.includes('hianime.to#') ||
                                        href.endsWith('/#') || href.endsWith('#')) {
                                        console.log('Blocked fragment link click:', href);
                                        e.preventDefault();
                                        e.stopPropagation();
                                        return false;
                                    }
                                    return originalListener.call(this, e);
                                };
                            }
                            return originalAddEventListener.call(this, type, listener, options);
                        };

                        // Remove existing ad elements periodically
                        setInterval(function() {
                            var adSelectors = [
                                '[id*="ad"]', '[class*="ad"]', '[id*="popup"]', '[class*="popup"]',
                                '[id*="banner"]', '[class*="banner"]', '[id*="sponsor"]', '[class*="sponsor"]',
                                'iframe[src*="ads"]', 'iframe[src*="doubleclick"]'
                            ];

                            adSelectors.forEach(function(selector) {
                                var elements = document.querySelectorAll(selector);
                                elements.forEach(function(el) {
                                    if (el && el.parentNode) {
                                        el.parentNode.removeChild(el);
                                    }
                                });
                            });
                        }, 1000);

                        console.log('HiAnime Ad Blocker: Initialized');
                    })();
                """.trimIndent(), null)
            }

            override fun shouldInterceptRequest(view: WebView?, request: android.webkit.WebResourceRequest?): android.webkit.WebResourceResponse? {
                val url = request?.url?.toString() ?: return null

                // Don't block legitimate hianime.to resources (images, CSS, JS)
                if (url.contains("hianime.to", ignoreCase = true)) {
                    // Allow all hianime.to resources except fragment URLs
                    if (url.endsWith("/#") || url.endsWith("#") ||
                        url.contains("hianime.to/#") || url.contains("hianime.to#")) {
                        // Block fragment-only URLs
                        return android.webkit.WebResourceResponse("text/plain", "utf-8", java.io.ByteArrayInputStream("".toByteArray()))
                    }
                    // Allow legitimate resources (images, CSS, JS, etc.)
                    return super.shouldInterceptRequest(view, request)
                }

                // Block external ad requests at network level
                val adBlockPatterns = listOf(
                    "googleads", "googlesyndication", "doubleclick", "adsystem",
                    "amazon-adsystem", "facebook.com/tr", "google-analytics",
                    "googletagmanager", "scorecardresearch", "quantserve",
                    "outbrain", "taboola", "revcontent", "mgid", "disqus",
                    "popunder", "popup", "interstitial"
                )

                val isExternalAdRequest = adBlockPatterns.any { pattern ->
                    url.contains(pattern, ignoreCase = true)
                }

                if (isExternalAdRequest) {
                    // Return empty response to block external ad requests
                    return android.webkit.WebResourceResponse("text/plain", "utf-8", java.io.ByteArrayInputStream("".toByteArray()))
                }

                return super.shouldInterceptRequest(view, request)
            }
        }
        
        // Set WebChromeClient for better JavaScript support and popup blocking
        webView.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                // You can add a progress bar here if needed
            }

            override fun onCreateWindow(
                view: WebView?,
                isDialog: Boolean,
                isUserGesture: Boolean,
                resultMsg: android.os.Message?
            ): Boolean {
                // Block all popup windows
                return false
            }

            override fun onJsAlert(
                view: WebView?,
                url: String?,
                message: String?,
                result: android.webkit.JsResult?
            ): Boolean {
                // Block JavaScript alerts that might be used for ads
                result?.cancel()
                return true
            }

            override fun onJsConfirm(
                view: WebView?,
                url: String?,
                message: String?,
                result: android.webkit.JsResult?
            ): Boolean {
                // Block JavaScript confirms that might be used for ads
                result?.cancel()
                return true
            }
        }
    }
    
    override fun onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            super.onBackPressed()
        }
    }
    
    override fun onResume() {
        super.onResume()
        webView.onResume()
    }
    
    override fun onPause() {
        super.onPause()
        webView.onPause()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        webView.destroy()
    }
}
