package com.example.hianime

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat

class MainActivity : AppCompatActivity() {
    
    private lateinit var webView: WebView
    
    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Enable full screen mode and hide system UI
        setupFullScreen()
        
        setContentView(R.layout.activity_main)
        
        webView = findViewById(R.id.webview)
        
        // Configure WebView settings
        setupWebView()
        
        // Load HiAnime website
        webView.loadUrl("https://hianime.to")
    }
    
    private fun setupFullScreen() {
        // Hide the status bar and navigation bar
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        windowInsetsController.let { controller ->
            controller.hide(WindowInsetsCompat.Type.systemBars())
            controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
        
        // Keep screen on while using the app
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        
        // Handle notch areas
        window.attributes.layoutInDisplayCutoutMode = 
            WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
    }
    
    @SuppressLint("SetJavaScriptEnabled")
    private fun setupWebView() {
        webView.settings.apply {
            // Enable JavaScript
            javaScriptEnabled = true
            
            // Enable DOM storage
            domStorageEnabled = true
            
            // Enable database storage
            databaseEnabled = true
            
            // App cache is deprecated, using default cache behavior
            
            // Set cache mode
            cacheMode = WebSettings.LOAD_DEFAULT
            
            // Enable zoom controls but hide zoom buttons
            setSupportZoom(true)
            builtInZoomControls = true
            displayZoomControls = false
            
            // Enable responsive design
            useWideViewPort = true
            loadWithOverviewMode = true
            
            // Set user agent to desktop to get full site
            userAgentString = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            
            // Enable mixed content (HTTP and HTTPS)
            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            
            // Enable hardware acceleration
            setRenderPriority(WebSettings.RenderPriority.HIGH)
            
            // Allow file access
            allowFileAccess = true
            allowContentAccess = true
            
            // Enable geolocation
            setGeolocationEnabled(true)

            // Block popups and unwanted content
            javaScriptCanOpenWindowsAutomatically = false
            setSupportMultipleWindows(false)
        }
        
        // Set WebViewClient to handle page navigation and block unwanted redirects
        webView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                return if (url != null) {
                    // Allow only hianime.to domain and its subdomains
                    val allowedDomains = listOf("hianime.to", "www.hianime.to")
                    val isAllowedDomain = allowedDomains.any { domain ->
                        url.contains(domain, ignoreCase = true)
                    }

                    if (isAllowedDomain) {
                        // Block specific popup/redirect patterns
                        val blockedPatterns = listOf(
                            "/#",  // Block fragment-only redirects that might be popups
                            "/popup",
                            "/ad",
                            "/ads",
                            "/redirect",
                            "javascript:",
                            "about:blank"
                        )

                        val isBlockedPattern = blockedPatterns.any { pattern ->
                            url.contains(pattern, ignoreCase = true)
                        }

                        if (isBlockedPattern) {
                            // Block this URL
                            return true
                        }

                        // Allow the URL to load in WebView
                        false
                    } else {
                        // Block all external domains
                        true
                    }
                } else {
                    true // Block if URL is null
                }
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                // Inject CSS and JavaScript to block popups and clean up the interface
                view?.evaluateJavascript("""
                    (function() {
                        // Add CSS to hide potential popup elements and clean interface
                        var style = document.createElement('style');
                        style.innerHTML = `
                            body { margin: 0 !important; padding: 0 !important; }
                            .popup, .modal, .overlay, .advertisement, .ad-banner { display: none !important; }
                            [id*="popup"], [class*="popup"], [id*="modal"], [class*="modal"] { display: none !important; }
                            [id*="ad"], [class*="ad"], [id*="banner"], [class*="banner"] { display: none !important; }
                        `;
                        document.head.appendChild(style);

                        // Override window.open to prevent popups
                        window.open = function() { return null; };

                        // Block common popup methods
                        window.alert = function() { return false; };
                        window.confirm = function() { return false; };

                        // Remove any existing popup event listeners
                        document.addEventListener('click', function(e) {
                            var target = e.target;
                            if (target && (target.href || target.onclick)) {
                                var href = target.href || '';
                                if (href.includes('#') && href.split('#')[1] === '') {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    return false;
                                }
                            }
                        }, true);
                    })();
                """.trimIndent(), null)
            }
        }
        
        // Set WebChromeClient for better JavaScript support and popup blocking
        webView.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                // You can add a progress bar here if needed
            }

            override fun onCreateWindow(
                view: WebView?,
                isDialog: Boolean,
                isUserGesture: Boolean,
                resultMsg: android.os.Message?
            ): Boolean {
                // Block all popup windows
                return false
            }

            override fun onJsAlert(
                view: WebView?,
                url: String?,
                message: String?,
                result: android.webkit.JsResult?
            ): Boolean {
                // Block JavaScript alerts that might be used for ads
                result?.cancel()
                return true
            }

            override fun onJsConfirm(
                view: WebView?,
                url: String?,
                message: String?,
                result: android.webkit.JsResult?
            ): Boolean {
                // Block JavaScript confirms that might be used for ads
                result?.cancel()
                return true
            }
        }
    }
    
    override fun onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            super.onBackPressed()
        }
    }
    
    override fun onResume() {
        super.onResume()
        webView.onResume()
    }
    
    override fun onPause() {
        super.onPause()
        webView.onPause()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        webView.destroy()
    }
}
