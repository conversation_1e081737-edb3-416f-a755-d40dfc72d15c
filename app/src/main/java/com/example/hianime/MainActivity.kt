package com.example.hianime

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat

class MainActivity : AppCompatActivity() {
    
    private lateinit var webView: WebView
    
    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Enable full screen mode and hide system UI
        setupFullScreen()
        
        setContentView(R.layout.activity_main)
        
        webView = findViewById(R.id.webview)
        
        // Configure WebView settings
        setupWebView()
        
        // Load HiAnime website
        webView.loadUrl("https://hianime.to")
    }
    
    private fun setupFullScreen() {
        // Hide the status bar and navigation bar
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        windowInsetsController.let { controller ->
            controller.hide(WindowInsetsCompat.Type.systemBars())
            controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
        
        // Keep screen on while using the app
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        
        // Handle notch areas
        window.attributes.layoutInDisplayCutoutMode = 
            WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
    }
    
    @SuppressLint("SetJavaScriptEnabled")
    private fun setupWebView() {
        webView.settings.apply {
            // Enable JavaScript
            javaScriptEnabled = true
            
            // Enable DOM storage
            domStorageEnabled = true
            
            // Enable database storage
            databaseEnabled = true
            
            // App cache is deprecated, using default cache behavior
            
            // Set cache mode
            cacheMode = WebSettings.LOAD_DEFAULT
            
            // Enable zoom controls but hide zoom buttons
            setSupportZoom(true)
            builtInZoomControls = true
            displayZoomControls = false
            
            // Enable responsive design
            useWideViewPort = true
            loadWithOverviewMode = true
            
            // Set user agent to desktop to get full site
            userAgentString = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            
            // Enable mixed content (HTTP and HTTPS)
            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            
            // Enable hardware acceleration
            setRenderPriority(WebSettings.RenderPriority.HIGH)
            
            // Allow file access
            allowFileAccess = true
            allowContentAccess = true
            
            // Enable geolocation
            setGeolocationEnabled(true)

            // Block popups and unwanted content
            javaScriptCanOpenWindowsAutomatically = false
            setSupportMultipleWindows(false)
        }
        
        // Set WebViewClient to handle page navigation and block unwanted redirects
        webView.webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                return if (url != null) {
                    // Comprehensive ad blocking patterns
                    val adBlockPatterns = listOf(
                        // Ad networks
                        "googleads", "googlesyndication", "doubleclick", "adsystem",
                        "amazon-adsystem", "facebook.com/tr", "google-analytics",
                        "googletagmanager", "scorecardresearch", "quantserve",
                        "outbrain", "taboola", "revcontent", "mgid", "disqus",
                        // Popup patterns
                        "popunder", "popup", "pop-up", "interstitial",
                        // Redirect patterns
                        "redirect", "redir", "click", "track", "analytics",
                        // Ad-specific patterns
                        "/ads/", "/ad/", "advertisement", "banner", "sponsor",
                        // Fragment-only URLs (common popup trigger)
                        "/#", "/##", "javascript:", "about:blank",
                        // Suspicious domains
                        "bit.ly", "tinyurl", "short.link", "t.co"
                    )

                    // Check if URL contains ad/popup patterns
                    val isAdOrPopup = adBlockPatterns.any { pattern ->
                        url.contains(pattern, ignoreCase = true)
                    }

                    if (isAdOrPopup) {
                        // Completely block ad/popup URLs - don't load anything
                        return true
                    }

                    // Allow only hianime.to domain and its subdomains
                    val allowedDomains = listOf("hianime.to", "www.hianime.to")
                    val isAllowedDomain = allowedDomains.any { domain ->
                        url.contains(domain, ignoreCase = true)
                    }

                    if (isAllowedDomain) {
                        // Even for allowed domains, block fragment-only URLs (popups)
                        if (url.endsWith("/#") || url.endsWith("#") ||
                            url.contains("hianime.to/#") || url.contains("hianime.to#")) {
                            // Block these popup URLs completely
                            return true
                        }
                        // Allow legitimate hianime URLs to load
                        false
                    } else {
                        // For external domains, don't load and stay on current page
                        // This prevents blank screens
                        return true
                    }
                } else {
                    true // Block if URL is null
                }
            }

            override fun onPageStarted(view: WebView?, url: String?, favicon: android.graphics.Bitmap?) {
                super.onPageStarted(view, url, favicon)
                // Additional check when page starts loading
                if (url != null) {
                    val adBlockPatterns = listOf(
                        "googleads", "googlesyndication", "doubleclick", "adsystem",
                        "amazon-adsystem", "facebook.com/tr", "google-analytics",
                        "popunder", "popup", "redirect", "/ads/", "/ad/"
                    )

                    val isAdOrPopup = adBlockPatterns.any { pattern ->
                        url.contains(pattern, ignoreCase = true)
                    }

                    if (isAdOrPopup) {
                        // Stop loading the page
                        view?.stopLoading()
                    }
                }
            }

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                // Inject comprehensive ad blocking and popup prevention
                view?.evaluateJavascript("""
                    (function() {
                        // Selective CSS ad blocking - preserve legitimate content and navigation
                        var style = document.createElement('style');
                        style.innerHTML = `
                            /* Basic layout fixes */
                            body { margin: 0 !important; padding: 0 !important; }

                            /* Hide specific ad elements without affecting anime covers or navigation */
                            .popup, .modal, .overlay:not(.anime-cover):not(.poster),
                            .advertisement, .ad-banner, .ads:not(.anime-list):not(.anime-grid),
                            .interstitial, .popunder, .pop-up,
                            [id*="popup"], [class*="popup"], [id*="modal"], [class*="modal"],
                            [id*="advertisement"], [class*="advertisement"],
                            [id*="sponsor"], [class*="sponsor"],
                            [id*="promo"]:not([class*="anime"]), [class*="promo"]:not([class*="anime"]),
                            iframe[src*="ads"], iframe[src*="doubleclick"], iframe[src*="googlesyndication"],
                            iframe[src*="amazon-adsystem"], iframe[src*="facebook.com/tr"],
                            script[src*="ads"], script[src*="doubleclick"], script[src*="googlesyndication"],
                            /* Block high z-index overlays but preserve legitimate modals and navigation */
                            div[style*="position: fixed"][style*="z-index: 999"]:not([class*="player"]):not([class*="video"]):not([class*="nav"]):not([class*="header"]),
                            div[style*="position: fixed"][style*="z-index: 9999"]:not([class*="player"]):not([class*="video"]):not([class*="nav"]):not([class*="header"]) {
                                display: none !important;
                                visibility: hidden !important;
                                opacity: 0 !important;
                                width: 0 !important;
                                height: 0 !important;
                            }

                            /* Ensure anime images and covers are visible */
                            img[src*="hianime"], .anime-cover img, .poster img,
                            .anime-item img, .film-poster img, .movie-item img {
                                display: block !important;
                                visibility: visible !important;
                                opacity: 1 !important;
                            }

                            /* Force navigation bar to stay visible */
                            .navbar, .nav-bar, .navigation, .header, .top-bar,
                            [class*="nav"], [class*="header"], [id*="nav"], [id*="header"],
                            .site-header, .main-header, .page-header {
                                display: block !important;
                                visibility: visible !important;
                                opacity: 1 !important;
                                position: relative !important;
                            }

                            /* Prevent video player from hiding navigation */
                            .video-player, .player-container, [class*="player"] {
                                position: relative !important;
                            }


                        `;
                        document.head.appendChild(style);

                        // Override and block popup functions
                        var originalOpen = window.open;
                        window.open = function(url, name, specs) {
                            console.log('Blocked popup attempt:', url);
                            return null;
                        };

                        // Block alert/confirm dialogs
                        window.alert = function(msg) {
                            console.log('Blocked alert:', msg);
                            return false;
                        };
                        window.confirm = function(msg) {
                            console.log('Blocked confirm:', msg);
                            return false;
                        };

                        // Block setTimeout/setInterval for ads
                        var originalSetTimeout = window.setTimeout;
                        var originalSetInterval = window.setInterval;

                        window.setTimeout = function(func, delay) {
                            if (typeof func === 'string' && (
                                func.includes('popup') || func.includes('ad') ||
                                func.includes('redirect') || func.includes('window.open')
                            )) {
                                console.log('Blocked setTimeout ad script');
                                return 0;
                            }
                            return originalSetTimeout.apply(this, arguments);
                        };

                        window.setInterval = function(func, delay) {
                            if (typeof func === 'string' && (
                                func.includes('popup') || func.includes('ad') ||
                                func.includes('redirect') || func.includes('window.open')
                            )) {
                                console.log('Blocked setInterval ad script');
                                return 0;
                            }
                            return originalSetInterval.apply(this, arguments);
                        };

                        // Block click events on suspicious links - enhanced for hianime.to/#
                        document.addEventListener('click', function(e) {
                            var target = e.target;
                            while (target && target !== document) {
                                var href = target.href || '';
                                var onclick = target.onclick ? target.onclick.toString() : '';

                                // Specifically block hianime.to/# and hianime.to# patterns
                                if (href.includes('hianime.to/#') || href.includes('hianime.to#') ||
                                    href.endsWith('/#') || href.endsWith('#') ||
                                    (href.includes('#') && href.split('#')[1] === '') ||
                                    href.includes('popup') || href.includes('redirect') ||
                                    onclick.includes('window.open') || onclick.includes('popup')) {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    e.stopImmediatePropagation();
                                    console.log('Blocked popup click:', href);
                                    return false;
                                }
                                target = target.parentElement;
                            }
                        }, true);

                        // Additional protection: Override link clicks that lead to fragments
                        var originalAddEventListener = Element.prototype.addEventListener;
                        Element.prototype.addEventListener = function(type, listener, options) {
                            if (type === 'click') {
                                var originalListener = listener;
                                listener = function(e) {
                                    var href = this.href || '';
                                    if (href.includes('hianime.to/#') || href.includes('hianime.to#') ||
                                        href.endsWith('/#') || href.endsWith('#')) {
                                        console.log('Blocked fragment link click:', href);
                                        e.preventDefault();
                                        e.stopPropagation();
                                        return false;
                                    }
                                    return originalListener.call(this, e);
                                };
                            }
                            return originalAddEventListener.call(this, type, listener, options);
                        };

                        // Monitor and maintain navigation bar visibility
                        setInterval(function() {
                            // Remove ad elements but preserve navigation
                            var adSelectors = [
                                '[id*="popup"]', '[class*="popup"]',
                                '[id*="banner"]:not([class*="nav"]):not([class*="header"])',
                                '[class*="banner"]:not([class*="nav"]):not([class*="header"])',
                                '[id*="sponsor"]', '[class*="sponsor"]',
                                'iframe[src*="ads"]', 'iframe[src*="doubleclick"]'
                            ];

                            adSelectors.forEach(function(selector) {
                                var elements = document.querySelectorAll(selector);
                                elements.forEach(function(el) {
                                    // Don't remove if it's part of navigation
                                    var isNavigation = el.closest('.navbar, .nav-bar, .navigation, .header, .top-bar, [class*="nav"], [class*="header"]');
                                    if (el && el.parentNode && !isNavigation) {
                                        el.parentNode.removeChild(el);
                                    }
                                });
                            });

                            // Ensure navigation bar stays visible
                            var navSelectors = [
                                '.navbar', '.nav-bar', '.navigation', '.header', '.top-bar',
                                '[class*="nav"]', '[class*="header"]', '[id*="nav"]', '[id*="header"]',
                                '.site-header', '.main-header', '.page-header'
                            ];

                            navSelectors.forEach(function(selector) {
                                var navElements = document.querySelectorAll(selector);
                                navElements.forEach(function(nav) {
                                    if (nav && nav.style) {
                                        nav.style.display = 'block';
                                        nav.style.visibility = 'visible';
                                        nav.style.opacity = '1';
                                        // Prevent it from being positioned off-screen
                                        if (nav.style.position === 'absolute' || nav.style.position === 'fixed') {
                                            nav.style.top = nav.style.top === '-9999px' ? '0' : nav.style.top;
                                            nav.style.left = nav.style.left === '-9999px' ? '0' : nav.style.left;
                                        }
                                    }
                                });
                            });
                        }, 1000);

                        // Protect against video player hiding navigation
                        document.addEventListener('fullscreenchange', function() {
                            setTimeout(function() {
                                var navSelectors = [
                                    '.navbar', '.nav-bar', '.navigation', '.header', '.top-bar',
                                    '[class*="nav"]', '[class*="header"]'
                                ];

                                navSelectors.forEach(function(selector) {
                                    var navElements = document.querySelectorAll(selector);
                                    navElements.forEach(function(nav) {
                                        if (nav) {
                                            nav.style.display = 'block';
                                            nav.style.visibility = 'visible';
                                            nav.style.opacity = '1';
                                        }
                                    });
                                });
                            }, 100);
                        });

                        // Monitor for video events that might hide navigation
                        document.addEventListener('DOMContentLoaded', function() {
                            var videos = document.querySelectorAll('video, iframe[src*="player"]');
                            videos.forEach(function(video) {
                                video.addEventListener('play', function() {
                                    setTimeout(function() {
                                        // Restore navigation after video starts
                                        var navElements = document.querySelectorAll('.navbar, .nav-bar, .navigation, .header, [class*="nav"], [class*="header"]');
                                        navElements.forEach(function(nav) {
                                            if (nav) {
                                                nav.style.display = 'block';
                                                nav.style.visibility = 'visible';
                                                nav.style.opacity = '1';
                                            }
                                        });
                                    }, 500);
                                });
                            });
                        });

                        // COMPREHENSIVE POPUP/AD BLOCKING SYSTEM

                        // AGGRESSIVE POPUP BLOCKING - Override all popup methods
                        window.originalOpen = window.open;
                        window.open = function(url, name, specs) {
                            console.log('🚫 BLOCKED popup attempt:', url);
                            return null;
                        };

                        // Block all alert/confirm/prompt dialogs
                        window.originalAlert = window.alert;
                        window.originalConfirm = window.confirm;
                        window.originalPrompt = window.prompt;

                        window.alert = function(msg) {
                            console.log('🚫 BLOCKED alert:', msg);
                            return false;
                        };
                        window.confirm = function(msg) {
                            console.log('🚫 BLOCKED confirm:', msg);
                            return false;
                        };
                        window.prompt = function(msg) {
                            console.log('🚫 BLOCKED prompt:', msg);
                            return null;
                        };

                        // Block setTimeout/setInterval with suspicious content
                        var originalSetTimeout = window.setTimeout;
                        var originalSetInterval = window.setInterval;

                        window.setTimeout = function(func, delay) {
                            var funcStr = func.toString ? func.toString() : String(func);
                            var suspiciousPatterns = [
                                'popup', 'window.open', 'alert', 'confirm', 'redirect',
                                'location.href', 'location.replace', 'opera', 'gx',
                                'advertisement', 'promo', 'sponsor'
                            ];

                            if (suspiciousPatterns.some(pattern => funcStr.toLowerCase().includes(pattern))) {
                                console.log('🚫 BLOCKED suspicious setTimeout:', funcStr.substring(0, 100));
                                return 0;
                            }
                            return originalSetTimeout.apply(this, arguments);
                        };

                        window.setInterval = function(func, delay) {
                            var funcStr = func.toString ? func.toString() : String(func);
                            var suspiciousPatterns = [
                                'popup', 'window.open', 'alert', 'confirm', 'redirect',
                                'location.href', 'location.replace', 'opera', 'gx',
                                'advertisement', 'promo', 'sponsor'
                            ];

                            if (suspiciousPatterns.some(pattern => funcStr.toLowerCase().includes(pattern))) {
                                console.log('🚫 BLOCKED suspicious setInterval:', funcStr.substring(0, 100));
                                return 0;
                            }
                            return originalSetInterval.apply(this, arguments);
                        };

                        // COMPREHENSIVE CLICK EVENT BLOCKING
                        document.addEventListener('click', function(e) {
                            var target = e.target;
                            var element = target;

                            // Check element and all parents for suspicious patterns
                            while (element && element !== document) {
                                var className = element.className || '';
                                var id = element.id || '';
                                var href = element.href || '';
                                var onclick = element.onclick ? element.onclick.toString() : '';
                                var textContent = element.textContent || '';

                                // Comprehensive blocking patterns
                                var blockPatterns = [
                                    // Fragment URLs
                                    'hianime.to/#', 'hianime.to#', '/#', '#',
                                    // Popup/Modal patterns
                                    'popup', 'modal', 'overlay', 'interstitial',
                                    // Ad patterns
                                    'advertisement', 'ad-banner', 'sponsor', 'promo',
                                    // Opera GX specific
                                    'opera', 'gx', 'browser', 'gaming',
                                    // Generic ad terms
                                    'download', 'install', 'get-now', 'click-here',
                                    // Suspicious functions
                                    'window.open', 'location.href', 'redirect'
                                ];

                                var elementText = (className + ' ' + id + ' ' + href + ' ' + onclick + ' ' + textContent).toLowerCase();
                                var isBlocked = blockPatterns.some(pattern => elementText.includes(pattern.toLowerCase()));

                                if (isBlocked) {
                                    console.log('🚫 BLOCKED click on suspicious element:', {
                                        tag: element.tagName,
                                        class: className,
                                        id: id,
                                        href: href,
                                        text: textContent.substring(0, 50)
                                    });

                                    e.preventDefault();
                                    e.stopPropagation();
                                    e.stopImmediatePropagation();

                                    return false;
                                }

                                element = element.parentElement;
                            }
                        }, true);

                        // BLOCK FOCUS EVENTS that might trigger popups
                        document.addEventListener('focus', function(e) {
                            var element = e.target;
                            if (element && element.href && (
                                element.href.includes('/#') ||
                                element.href.includes('opera') ||
                                element.href.includes('popup')
                            )) {
                                console.log('🚫 BLOCKED focus on suspicious link:', element.href);
                                e.preventDefault();
                                element.blur();
                            }
                        }, true);

                        console.log('🛡️ HiAnime ULTIMATE AD BLOCKER: Initialized with comprehensive popup/ad blocking at all levels!');
                    })();
                """.trimIndent(), null)
            }

            override fun shouldInterceptRequest(view: WebView?, request: android.webkit.WebResourceRequest?): android.webkit.WebResourceResponse? {
                val url = request?.url?.toString() ?: return null

                // COMPREHENSIVE AD BLOCKING PATTERNS
                val comprehensiveAdBlockList = listOf(
                    // Major ad networks
                    "googleads", "googlesyndication", "doubleclick", "adsystem", "adnxs",
                    "amazon-adsystem", "facebook.com/tr", "google-analytics", "googletagmanager",
                    "scorecardresearch", "quantserve", "outbrain", "taboola", "revcontent",
                    "mgid", "disqus", "criteo", "pubmatic", "openx", "rubiconproject",

                    // Popup/redirect networks
                    "popunder", "popup", "interstitial", "redirect", "clickfunnels",
                    "smartadserver", "adsystem", "adform", "adsystem", "adtech",

                    // Opera/Browser ads
                    "opera.com", "operacdn", "opera-api", "operasoftware",
                    "browser-update", "browser-download", "browser-install",

                    // Tracking and analytics
                    "analytics", "tracking", "metrics", "telemetry", "stats",
                    "hotjar", "mixpanel", "segment", "amplitude", "fullstory",

                    // Social media trackers
                    "facebook.com/tr", "twitter.com/i/adsct", "linkedin.com/px",
                    "pinterest.com/ct", "snapchat.com/tr", "tiktok.com/i18n",

                    // Generic ad patterns
                    "/ads/", "/ad/", "/advertisement/", "/banner/", "/popup/",
                    "/promo/", "/sponsor/", "/affiliate/", "/tracking/",

                    // Suspicious domains
                    "bit.ly", "tinyurl", "short.link", "t.co", "goo.gl",
                    "ow.ly", "buff.ly", "tiny.cc", "is.gd"
                )

                // Check if URL matches any blocking pattern
                val isBlockedRequest = comprehensiveAdBlockList.any { pattern ->
                    url.contains(pattern, ignoreCase = true)
                }

                if (isBlockedRequest) {
                    println("🚫 NETWORK BLOCKED: $url")
                    return android.webkit.WebResourceResponse("text/plain", "utf-8", java.io.ByteArrayInputStream("".toByteArray()))
                }

                // Special handling for hianime.to domain
                if (url.contains("hianime.to", ignoreCase = true)) {
                    // Block fragment-only URLs even from hianime domain
                    if (url.endsWith("/#") || url.endsWith("#") ||
                        url.contains("hianime.to/#") || url.contains("hianime.to#")) {
                        println("🚫 BLOCKED fragment URL: $url")
                        return android.webkit.WebResourceResponse("text/plain", "utf-8", java.io.ByteArrayInputStream("".toByteArray()))
                    }

                    // Block suspicious query parameters
                    val suspiciousParams = listOf("popup", "ad", "promo", "redirect", "ref=", "utm_")
                    if (suspiciousParams.any { param -> url.contains(param, ignoreCase = true) }) {
                        println("🚫 BLOCKED suspicious hianime URL: $url")
                        return android.webkit.WebResourceResponse("text/plain", "utf-8", java.io.ByteArrayInputStream("".toByteArray()))
                    }
                }

                return super.shouldInterceptRequest(view, request)
            }
        }
        
        // No JavaScript interface needed - pure ad blocking approach

        // Set WebChromeClient for better JavaScript support and popup blocking
        webView.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                // You can add a progress bar here if needed
            }

            override fun onCreateWindow(
                view: WebView?,
                isDialog: Boolean,
                isUserGesture: Boolean,
                resultMsg: android.os.Message?
            ): Boolean {
                // Block all popup windows
                return false
            }

            override fun onJsAlert(
                view: WebView?,
                url: String?,
                message: String?,
                result: android.webkit.JsResult?
            ): Boolean {
                // Block JavaScript alerts that might be used for ads
                result?.cancel()
                return true
            }

            override fun onJsConfirm(
                view: WebView?,
                url: String?,
                message: String?,
                result: android.webkit.JsResult?
            ): Boolean {
                // Block JavaScript confirms that might be used for ads
                result?.cancel()
                return true
            }
        }
    }
    
    override fun onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            super.onBackPressed()
        }
    }
    
    override fun onResume() {
        super.onResume()
        webView.onResume()
    }
    
    override fun onPause() {
        super.onPause()
        webView.onPause()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        webView.destroy()
    }
}


